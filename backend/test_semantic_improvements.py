#!/usr/bin/env python3
"""
Test script to verify the semantic understanding improvements in the code embedding service.
This script tests the enhanced test case ID extraction and semantic filtering.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.code_embedding_service import CodeEmbeddingService
from app.models.project import ProgrammingLanguage

def test_test_case_id_extraction():
    """Test the enhanced test case ID extraction patterns."""
    service = CodeEmbeddingService()
    
    test_cases = [
        # Standard patterns
        ("test26_LoginWithValidCredentials", "TC-026"),
        ("test_26_LoginWithValidCredentials", "TC-026"),
        ("async test26_LoginWithValidCredentials", "TC-026"),
        ("testTC001_LoginFlow", "TC-001"),
        ("test_TC_001_LoginFlow", "TC-001"),
        ("tc001_LoginFlow", "TC-001"),
        ("tc_001_LoginFlow", "TC-001"),
        ("testCase26_LoginFlow", "TC-026"),
        ("test_case_26_LoginFlow", "TC-026"),
        
        # Edge cases
        ("26_test_LoginFlow", "TC-026"),
        ("test_login_with_valid_credentials_26", "TC-026"),
        ("testLoginFlow123", "TC-123"),
        
        # Should return TC-UNKNOWN
        ("test_login_without_numbers", "TC-UNKNOWN"),
        ("login_test", "TC-UNKNOWN"),
    ]
    
    print("🧪 Testing Test Case ID Extraction:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for method_name, expected in test_cases:
        result = service._extract_test_case_id_from_method_name(method_name)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} {method_name:<40} -> {result:<10} (expected: {expected})")
        
        if result == expected:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 Results: {passed} passed, {failed} failed")
    return failed == 0

def test_method_name_to_title():
    """Test the enhanced method name to title conversion."""
    service = CodeEmbeddingService()
    
    test_cases = [
        ("test26_LoginWithValidCredentials", "Login With Valid Credentials"),
        ("test_26_LoginWithValidCredentials", "Login With Valid Credentials"),
        ("testTC001_LoginFlow", "Login Flow"),
        ("async_test_login_flow", "Login Flow"),
        ("tc001_user_registration", "User Registration"),
        ("test_case_26_checkout_process", "Checkout Process"),
        ("26_test_login_validation", "Login Validation"),
        ("testLoginWithEmptyPassword", "Login With Empty Password"),
    ]
    
    print("\n🧪 Testing Method Name to Title Conversion:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for method_name, expected in test_cases:
        result = service._method_name_to_title(method_name)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"{status} {method_name:<40} -> '{result}' (expected: '{expected}')")
        
        if result == expected:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 Results: {passed} passed, {failed} failed")
    return failed == 0

def test_title_similarity():
    """Test the title similarity calculation."""
    service = CodeEmbeddingService()
    
    test_cases = [
        # Exact matches
        ("Login With Valid Credentials", "Login With Valid Credentials", 1.0),
        
        # High similarity
        ("Login With Valid Credentials", "Login With Valid Password", 0.8),
        ("User Registration Flow", "User Registration Process", 0.7),
        
        # Medium similarity
        ("Login Test", "Login Validation", 0.5),
        
        # Low similarity
        ("Login Test", "Checkout Process", 0.2),
        ("Login", "Registration", 0.1),
        
        # No similarity
        ("Login", "Completely Different", 0.0),
    ]
    
    print("\n🧪 Testing Title Similarity Calculation:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for title1, title2, expected_min in test_cases:
        result = service._calculate_title_similarity(title1, title2)
        # Allow some tolerance for similarity calculations
        tolerance = 0.2
        status = "✅ PASS" if result >= (expected_min - tolerance) else "❌ FAIL"
        print(f"{status} '{title1}' vs '{title2}' -> {result:.2f} (expected: >={expected_min})")
        
        if result >= (expected_min - tolerance):
            passed += 1
        else:
            failed += 1
    
    print(f"\n📊 Results: {passed} passed, {failed} failed")
    return failed == 0

def test_enhanced_test_method_extraction():
    """Test the enhanced test method extraction."""
    service = CodeEmbeddingService()
    
    # Sample JavaScript test file content
    js_content = """
    class LoginTests {
        async test26_LoginWithValidCredentials() {
            // Test implementation
        }
        
        test27_LoginWithInvalidPassword() {
            // Test implementation
        }
        
        async testTC001_UserRegistration() {
            // Test implementation
        }
    }
    
    function test28_CheckoutProcess() {
        // Test implementation
    }
    
    const test29_PaymentFlow = async () => {
        // Test implementation
    };
    
    it('should login with valid credentials', () => {
        // Traditional test
    });
    """
    
    print("\n🧪 Testing Enhanced Test Method Extraction:")
    print("=" * 50)
    
    methods = service._extract_test_methods(js_content, ProgrammingLanguage.JAVASCRIPT)
    
    expected_methods = [
        "test26_LoginWithValidCredentials",
        "test27_LoginWithInvalidPassword", 
        "testTC001_UserRegistration",
        "test28_CheckoutProcess",
        "test29_PaymentFlow",
        "should login with valid credentials"
    ]
    
    print(f"Extracted methods: {methods}")
    print(f"Expected methods: {expected_methods}")
    
    # Check if we found the important test methods
    found_test_methods = [m for m in methods if m.startswith('test') or m.startswith('tc')]
    expected_test_methods = [m for m in expected_methods if m.startswith('test') or m.startswith('tc')]
    
    success = len(found_test_methods) >= len(expected_test_methods) - 1  # Allow some tolerance
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} Found {len(found_test_methods)} test methods (expected: ~{len(expected_test_methods)})")
    
    return success

def main():
    """Run all tests."""
    print("🚀 Testing Semantic Understanding Improvements")
    print("=" * 60)
    
    tests = [
        test_test_case_id_extraction,
        test_method_name_to_title,
        test_title_similarity,
        test_enhanced_test_method_extraction,
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"🏁 Final Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Semantic understanding improvements are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the improvements.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
